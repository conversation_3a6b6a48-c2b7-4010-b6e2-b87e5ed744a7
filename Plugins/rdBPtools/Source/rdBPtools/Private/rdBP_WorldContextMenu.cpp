//
// rdBP_BPOutlinerMenu.cpp
//
// Copyright (c) 2022 Recourse Design ltd. All rights reserved.
//
// Version 1.21
//
// Creation Date: 22nd September 2022
// Last Modified: 9th April 2024
//
#include "Runtime/Core/Public/Misc/ScopedSlowTask.h"
#include "Editor/UnrealEd/Public/ScopedTransaction.h"
#include "ToolMenus.h"
#include "SSceneOutliner.h"
#include "rdBPtoolsOptions.h"
#include "Dialogs/Dialogs.h"
#include "Misc/MessageDialog.h"
#include "Engine/Selection.h"
#include "Misc/ConfigCacheIni.h"
#include "LevelEditor.h"
#include "Engine/StaticMeshActor.h"
#include "Animation/SkeletalMeshActor.h"
#include "Misc/ConfigCacheIni.h"
#include "EngineUtils.h"
#include "ContentBrowserModule.h"
#include "IContentBrowserSingleton.h"
#if ENGINE_MAJOR_VERSION>4
#include "LevelEditorSubsystem.h"
#include "LevelInstance/LevelInstanceSubsystem.h"
#endif
#include "rdBPtoolsOptions.h"

#define LOCTEXT_NAMESPACE "FrdBPtoolsModule"

//----------------------------------------------------------------------------------------------------------------
// CopyToLevel
//----------------------------------------------------------------------------------------------------------------
void FrdBPTools::CopyToLevel() {

	rdBPClassList.Add(MakeShared<rdBPclass>());
	rdBPclass& newBPclass=rdBPClassList[rdBPClassList.Num()-1].Get();
	newBPclass.rdBPoptions=NewObject<UrdBPtoolsOptions>(GetTransientPackage());
	newBPclass.rdBPoptions->AddToRoot();

	if(!FConfigCacheIni::LoadGlobalIniFile(newBPclass.configIni,TEXT("rdBPToolsConfig"))) {
#ifdef _INCLUDE_DEBUGGING_STUFF
		UE_LOG(LogTemp, Display,TEXT("rdBPTools: Failed to load rdBPTools config ini file"));
#endif
	}

	TArray<AActor*> addedActors;
	newBPclass.CopyToLevelForSelectedActors(addedActors);
}

//----------------------------------------------------------------------------------------------------------------
// ReplaceSelectedBPs
//----------------------------------------------------------------------------------------------------------------
void FrdBPTools::ReplaceSelectedBPs() {

	rdBPClassList.Add(MakeShared<rdBPclass>());
	rdBPclass& newBPclass=rdBPClassList[rdBPClassList.Num()-1].Get();
	newBPclass.rdBPoptions=NewObject<UrdBPtoolsOptions>(GetTransientPackage());
	newBPclass.rdBPoptions->AddToRoot();

	if(!FConfigCacheIni::LoadGlobalIniFile(newBPclass.configIni,TEXT("rdBPToolsConfig"))) {
#ifdef _INCLUDE_DEBUGGING_STUFF
		UE_LOG(LogTemp, Display,TEXT("rdBPTools: Failed to load rdBPTools config ini file"));
#endif
	}
	TArray<AActor*> addedActors;
	newBPclass.ReplaceSelectedBPs(addedActors);
}

//----------------------------------------------------------------------------------------------------------------
// CreateFromLevel
//----------------------------------------------------------------------------------------------------------------
void FrdBPTools::CreateFromLevel() {

	rdBPClassList.Add(MakeShared<rdBPclass>());
	rdBPclass& newBPclass=rdBPClassList[rdBPClassList.Num()-1].Get();
	newBPclass.rdBPoptions=NewObject<UrdBPtoolsOptions>(GetTransientPackage());
	newBPclass.rdBPoptions->AddToRoot();

	if(!FConfigCacheIni::LoadGlobalIniFile(newBPclass.configIni,TEXT("rdBPToolsConfig"))) {
#ifdef _INCLUDE_DEBUGGING_STUFF
		UE_LOG(LogTemp, Display,TEXT("rdBPTools: Failed to load rdBPTools config ini file"));
#endif
	}

	newBPclass.CreateFromSelectedActors();
}

//----------------------------------------------------------------------------------------------------------------
// CreateFromFolder
//----------------------------------------------------------------------------------------------------------------
void FrdBPTools::CreateFromFolder() {

	rdBPClassList.Add(MakeShared<rdBPclass>());
	rdBPclass& newBPclass=rdBPClassList[rdBPClassList.Num()-1].Get();
	newBPclass.rdBPoptions=NewObject<UrdBPtoolsOptions>(GetTransientPackage());
	newBPclass.rdBPoptions->AddToRoot();

	if(!FConfigCacheIni::LoadGlobalIniFile(newBPclass.configIni,TEXT("rdBPToolsConfig"))) {
#ifdef _INCLUDE_DEBUGGING_STUFF
		UE_LOG(LogTemp, Display,TEXT("rdBPTools: Failed to load rdBPTools config ini file"));
#endif
	}

	// select all actors in the folder
	TArray<AActor*> actorList;
	FWorldContext* world=GEngine->GetWorldContextFromGameViewport(GEngine->GameViewport);
	UWorld* World=world->World();

	TWeakPtr<class ILevelEditor> LevelEditor=FModuleManager::GetModuleChecked<FLevelEditorModule>(TEXT("LevelEditor")).GetLevelEditorInstance();
	if(LevelEditor.IsValid()) {

#if ENGINE_MAJOR_VERSION>4 && ENGINE_MINOR_VERSION>0
		TSharedPtr<class ISceneOutliner> SceneOutlinerPtr=LevelEditor.Pin()->GetMostRecentlyUsedSceneOutliner();
#else
		TSharedPtr<class ISceneOutliner> SceneOutlinerPtr=LevelEditor.Pin()->GetSceneOutliner();
#endif
		if(SceneOutlinerPtr) {

#if ENGINE_MAJOR_VERSION>4
			STreeView<FSceneOutlinerTreeItemPtr>& tree=(STreeView<FSceneOutlinerTreeItemPtr>&)SceneOutlinerPtr->GetTree();
#else
			STreeView<SceneOutliner::FTreeItemPtr>& tree=(STreeView<SceneOutliner::FTreeItemPtr>&)SceneOutlinerPtr->GetTree();
#endif
			if(tree.GetSelectedItems().Num()>0) {

				auto itm=tree.GetSelectedItems()[0];

				if(itm && itm->GetParent()) {
				
					FString filename=itm->GetDisplayString();
					FString folder=itm->GetDisplayString();

					for(auto node=itm->GetParent();node.IsValid();node=node->GetParent()) {
						if(node->GetParent()) folder=node->GetDisplayString()+TEXT("/")+folder;
					}

					newBPclass.rdBPoptions->destFilename=TEXT("BP_")+filename;

					for(TActorIterator<AActor> it(World);it;++it) {
						AActor* actor=Cast<AActor>(*it);
						if(actor && !actor->GetParentComponent()) { // just main actors

#if ENGINE_MAJOR_VERSION>4
							FString nm=actor->GetFolder().GetPath().ToString();
#else
							FString nm=actor->GetFolderPath().ToString();
#endif
							if(nm.StartsWith(folder)) {
								actorList.Add(actor);
							}
						}
					} 
				} else {
					SGenericDialogWidget::OpenDialog(LOCTEXT("rdBPtoolsError","Error"),SNew(STextBlock).Text(FText::FromString(TEXT("Cannot create BP from Level Root Node"))),SGenericDialogWidget::FArguments(),true);
				}
			} else {
				SGenericDialogWidget::OpenDialog(LOCTEXT("rdBPtoolsError","Error"),SNew(STextBlock).Text(FText::FromString(TEXT("No Items Selected"))),SGenericDialogWidget::FArguments(),true);
			}
		}
	}

	if(actorList.Num()>0) {
		newBPclass.CreateFromActorList(actorList);
	}
}

//----------------------------------------------------------------------------------------------------------------
// ChangeRandomSettings
//----------------------------------------------------------------------------------------------------------------
void FrdBPTools::ChangeRandomActorSettings() {

	rdBPClassList.Add(MakeShared<rdBPclass>());
	rdBPclass& newBPclass=rdBPClassList[rdBPClassList.Num()-1].Get();
	newBPclass.rdBPoptions=NewObject<UrdBPtoolsOptions>(GetTransientPackage());
	newBPclass.rdBPoptions->AddToRoot();

	if(!FConfigCacheIni::LoadGlobalIniFile(newBPclass.configIni,TEXT("rdBPToolsConfig"))) {
#ifdef _INCLUDE_DEBUGGING_STUFF
		UE_LOG(LogTemp, Display,TEXT("rdBPTools: Failed to load rdBPTools config ini file"));
#endif
	}

	newBPclass.ChangeRandomActorSettings();
}

//----------------------------------------------------------------------------------------------------------------
// ChangeInstanceSettings
//----------------------------------------------------------------------------------------------------------------
void FrdBPTools::ChangeInstanceSettings() {

	rdBPClassList.Add(MakeShared<rdBPclass>());
	rdBPclass& newBPclass=rdBPClassList[rdBPClassList.Num()-1].Get();
	newBPclass.rdBPoptions=NewObject<UrdBPtoolsOptions>(GetTransientPackage());
	newBPclass.rdBPoptions->AddToRoot();

	if(!FConfigCacheIni::LoadGlobalIniFile(newBPclass.configIni,TEXT("rdBPToolsConfig"))) {
#ifdef _INCLUDE_DEBUGGING_STUFF
		UE_LOG(LogTemp, Display,TEXT("rdBPTools: Failed to load rdBPTools config ini file"));
#endif
	}

	newBPclass.ChangeInstanceSettings();
}

//----------------------------------------------------------------------------------------------------------------
// RemoveRandomSettings
//----------------------------------------------------------------------------------------------------------------
void FrdBPTools::RemoveRandomActorTags() {

	rdBPClassList.Add(MakeShared<rdBPclass>());
	rdBPclass& newBPclass=rdBPClassList[rdBPClassList.Num()-1].Get();
	newBPclass.rdBPoptions=NewObject<UrdBPtoolsOptions>(GetTransientPackage());
	newBPclass.rdBPoptions->AddToRoot();

	if(!FConfigCacheIni::LoadGlobalIniFile(newBPclass.configIni,TEXT("rdBPToolsConfig"))) {
#ifdef _INCLUDE_DEBUGGING_STUFF
		UE_LOG(LogTemp, Display,TEXT("rdBPTools: Failed to load rdBPTools config ini file"));
#endif
	}

	newBPclass.RemoveRandomSettingsForSelectedActors();
}

//----------------------------------------------------------------------------------------------------------------
// CreateSpawnActorFromSelection
//----------------------------------------------------------------------------------------------------------------
void FrdBPTools::CreateSpawnActorFromSelection() {

	rdBPClassList.Add(MakeShared<rdBPclass>());
	rdBPclass& newBPclass=rdBPClassList[rdBPClassList.Num()-1].Get();
	newBPclass.rdBPoptions=NewObject<UrdBPtoolsOptions>(GetTransientPackage());
	newBPclass.rdBPoptions->AddToRoot();

	if(!FConfigCacheIni::LoadGlobalIniFile(newBPclass.configIni,TEXT("rdBPToolsConfig"))) {
#ifdef _INCLUDE_DEBUGGING_STUFF
		UE_LOG(LogTemp, Display,TEXT("rdBPTools: Failed to load rdBPTools config ini file"));
#endif
	}

	newBPclass.CreateSpawnActorFromSelection();
}

//----------------------------------------------------------------------------------------------------------------
// UpdateSelectedPrefabsBPs
//----------------------------------------------------------------------------------------------------------------
void FrdBPTools::UpdateSelectedPrefabsBPs() {

	rdBPClassList.Add(MakeShared<rdBPclass>());
	rdBPclass& newBPclass=rdBPClassList[rdBPClassList.Num()-1].Get();
	newBPclass.rdBPoptions=NewObject<UrdBPtoolsOptions>(GetTransientPackage());
	newBPclass.rdBPoptions->AddToRoot();

	if(!FConfigCacheIni::LoadGlobalIniFile(newBPclass.configIni,TEXT("rdBPToolsConfig"))) {
#ifdef _INCLUDE_DEBUGGING_STUFF
		UE_LOG(LogTemp, Display,TEXT("rdBPTools: Failed to load rdBPTools config ini file"));
#endif
	}

	newBPclass.UpdateSelectedPrefabsBPs();
}

//----------------------------------------------------------------------------------------------------------------
// SetRelyOnActors
//----------------------------------------------------------------------------------------------------------------
void FrdBPTools::SetRelyOnActors() {

	rdBPClassList.Add(MakeShared<rdBPclass>());
	rdBPclass& newBPclass=rdBPClassList[rdBPClassList.Num()-1].Get();
	newBPclass.rdBPoptions=NewObject<UrdBPtoolsOptions>(GetTransientPackage());
	newBPclass.rdBPoptions->AddToRoot();
	newBPclass.bpEditor=nullptr;
	newBPclass.bp=nullptr;

	if(!FConfigCacheIni::LoadGlobalIniFile(newBPclass.configIni,TEXT("rdBPToolsConfig"))) {
#ifdef _INCLUDE_DEBUGGING_STUFF
		UE_LOG(LogTemp, Display,TEXT("rdBPTools: Failed to load rdBPTools config ini file"));
#endif
	}

	newBPclass.ChangeRelyOnActorsForSelectedActors();
}


//----------------------------------------------------------------------------------------------------------------
// RegisterMenus (Called on app init)
//----------------------------------------------------------------------------------------------------------------
void FrdBPTools::RegisterMenus() {

	// Owner will be used for cleanup in call to UToolMenus::UnregisterOwner
	FToolMenuOwnerScoped OwnerScoped(this);

	// LevelEditor Context Menu
	{
		UToolMenu* Menu=UToolMenus::Get()->ExtendMenu("LevelEditor.ActorContextMenu");
		{
#if ENGINE_MAJOR_VERSION>4			
			FToolMenuSection& Section=Menu->FindOrAddSection("ActorGeneral");
#else
			FToolMenuSection& Section=Menu->FindOrAddSection("ActorAsset");
#endif
			FSlateIcon Icon3=FSlateIcon(FPluginStyle::GetStyleSetName(),"rdBPTools.Icon","rdBPTools.Icon");
			Section.AddEntry(FToolMenuEntry::InitSubMenu("rdBPtools",LOCTEXT("rdBPtools_LevelMenu","rdBPtools"),LOCTEXT("rdBPtools_LevelMenu_Main_Tooltip1","rdBPtools SubMenu"),FNewToolMenuDelegate::CreateRaw(this,&FrdBPTools::LevelMenuSubMenu),false,Icon3));
		}
	}

	// World Outliner Menu
	{
		UToolMenu* Menu=UToolMenus::Get()->ExtendMenu("LevelEditor.SceneOutlinerContextMenu");
		{
			FToolMenuSection& Section=Menu->FindOrAddSection("ActorAsset");
			FSlateIcon Icon=FSlateIcon(FPluginStyle::GetStyleSetName(),"rdBPTools.Icon","rdBPTools.Icon");
			Section.AddEntry(FToolMenuEntry::InitSubMenu("rdBPtools",LOCTEXT("rdBPtools_LevelMenu","rdBPtools"),LOCTEXT("rdBPtools_LevelMenu_Tooltip1a","rdBPtools Outline SubMenu"),FNewToolMenuDelegate::CreateRaw(this,&FrdBPTools::OutlinerMenuSubMenu),false,Icon));
		}
	}

	// BPEditor Context Menu
	{
#if ENGINE_MAJOR_VERSION>4
		UToolMenu* Menu=UToolMenus::Get()->ExtendMenu("Kismet.SubobjectEditorContextMenu");
#else
		UToolMenu* Menu=UToolMenus::Get()->ExtendMenu("Kismet.SCSEditorContextMenu");
#endif
		{
			FToolMenuSection& Section=Menu->FindOrAddSection("BlueprintSCS");
			FSlateIcon Icon3=FSlateIcon(FPluginStyle::GetStyleSetName(),"rdBPTools.Icon","rdBPTools.Icon");
			Section.AddEntry(FToolMenuEntry::InitSubMenu("rdBPtools",LOCTEXT("rdBPtools_LevelMenu","rdBPtools"),LOCTEXT("rdBPtoolsBPEditorMenu_Main_Tooltip1","rdBPtools SubMenu"),FNewToolMenuDelegate::CreateRaw(this,&FrdBPTools::BPOutlinerSubMenu),false,Icon3));
		}
	}
}

//----------------------------------------------------------------------------------------------------------------
// OutlinerMenuSubMenu
//----------------------------------------------------------------------------------------------------------------
void FrdBPTools::OutlinerMenuSubMenu(UToolMenu* menu) {

	FToolMenuSection& Section=menu->AddSection("rdBPtoolsSec",LOCTEXT("rdBPtools_LevelMenu_Label","rdBPtools"));

	FUIAction Action1=FUIAction(FExecuteAction::CreateRaw(this,&FrdBPTools::CollapseBrowserAllButThis));
	FSlateIcon Icon1=FSlateIcon(FPluginStyle::GetStyleSetName(),"rdBPTools.Collapse","rdBPTools.Collapse");
	Section.AddEntry(FToolMenuEntry::InitMenuEntry("rdBPtools_sub1",LOCTEXT("rdBPtools_BPOutlineMenu1","Collapse all but this"),LOCTEXT("rdBPtools_BPOutlineMenu1_Tooltip1","Collapse All Folders except for this one"),Icon1,Action1));

	FUIAction Action2=FUIAction(FExecuteAction::CreateRaw(this,&FrdBPTools::CreateFromFolder));
	FSlateIcon Icon2=FSlateIcon(FPluginStyle::GetStyleSetName(),"rdBPTools.CreateFromFolder","rdBPTools.CreateFromFolder");
	Section.AddEntry(FToolMenuEntry::InitMenuEntry("rdBPtools2",LOCTEXT("rdBPtools_LevelMenu2","Create From Folder..."),LOCTEXT("rdBPtools_LevelMenu_Tooltip2","Create a Blueprint from the selected Actors"),Icon2,Action2));

//	FSlateIcon Icon4=FSlateIcon(FPluginStyle::GetStyleSetName(),"rdBPTools.ChangeMobility","rdBPTools.ChangeMobility");
//	Section.AddEntry(FToolMenuEntry::InitSubMenu("rdBPtools4",LOCTEXT("rdBPtools_LevelMenu4","Change Mobility"),LOCTEXT("rdBPtools_LevelMenu_Tooltip4","Change the Mobility of Components within the selected Blueprints"),
//												FNewToolMenuDelegate::CreateRaw(this,&FrdBPTools::MobilitySubMenu),false,Icon4));

}

//----------------------------------------------------------------------------------------------------------------
// LevelMenuSubMenu
//----------------------------------------------------------------------------------------------------------------
void FrdBPTools::LevelMenuSubMenu(UToolMenu* menu) {

	int32 numStaticMeshes=0,numSkelMeshes=0,numMisc=0;

	FToolMenuSection& Section=menu->AddSection("rdBPtoolsSec",LOCTEXT("rdBPtools_LevelMenu_Label","rdBPtools"));

	for(FSelectionIterator it(*GEditor->GetSelectedActors());it;++it) {

		UClass* c=(*it)->GetClass();
		UBlueprintGeneratedClass* bpClass=Cast<UBlueprintGeneratedClass>(c); // Blueprints
		if(bpClass) {
			UBlueprint* bp=Cast<UBlueprint>(bpClass->ClassGeneratedBy);
			if(bp) numSelectedBPs++;
			continue;
		}

		if((*it)->IsA(AStaticMeshActor::StaticClass())) { // Static Meshes
			AStaticMeshActor* staticMeshActor=Cast<AStaticMeshActor>(*it);
			numStaticMeshes++;
			continue;
		}

		if((*it)->IsA(ASkeletalMeshActor::StaticClass())) { // Skeletal Meshes
			ASkeletalMeshActor* skeletalMeshActor=Cast<ASkeletalMeshActor>(*it);
			numSkelMeshes++;
			continue;
		}

		numMisc++;
	}

	FSlateIcon Icon2=FSlateIcon(FPluginStyle::GetStyleSetName(),"rdBPTools.CreateFromLevel","rdBPTools.CreateFromLevel");
	Section.AddEntry(FToolMenuEntry::InitSubMenu("rdBPtools00",LOCTEXT("rdBPtools_LevelMenu2","Create"),LOCTEXT("rdBPtools_LevelMenu_Tooltip2","Create Submenu"),
												FNewToolMenuDelegate::CreateRaw(this,&FrdBPTools::CreateSubMenu),false,Icon2));

	FSlateIcon Icon8=FSlateIcon(FPluginStyle::GetStyleSetName(),"rdBPTools.HarvestInstances","rdBPTools.HarvestInstances");
	Section.AddEntry(FToolMenuEntry::InitSubMenu("rdBPtools01",LOCTEXT("rdBPtools_LevelMenu8","Harvest Instances"),LOCTEXT("rdBPtools_LevelMenu_Tooltip8","Convert any StaticMesh Instances in the selected assets to either StaticMeshes in the Level or to a Blueprint"),
												FNewToolMenuDelegate::CreateRaw(this,&FrdBPTools::HarvestInstancesSubMenu),false,Icon8));

	FSlateIcon Icon16=FSlateIcon(FPluginStyle::GetStyleSetName(),"rdBPTools.Conversion","rdBPTools.Conversion");
	Section.AddEntry(FToolMenuEntry::InitSubMenu("rdBPtools02",LOCTEXT("rdBPtools_LevelMenu17","Conversion"),LOCTEXT("rdBPtools_LevelMenu_Tooltip16","Conversion Submenu"),
												FNewToolMenuDelegate::CreateRaw(this,&FrdBPTools::ConversionSubMenu),false,Icon16));

	FSlateIcon Icon17=FSlateIcon(FPluginStyle::GetStyleSetName(),"rdBPTools.ChangeRandomSettings","rdBPTools.ChangeRandomSettings");
	Section.AddEntry(FToolMenuEntry::InitSubMenu("rdBPtools03",LOCTEXT("rdBPtools_LevelMenu17","Randomization"),LOCTEXT("rdBPtools_LevelMenu_Tooltip17","Randomization Submenu"),
												FNewToolMenuDelegate::CreateRaw(this,&FrdBPTools::RandomSubMenu),false,Icon17));



	if(numSelectedBPs>0) { // Only show if at least one blueprint actor is selected

		FUIAction Action15=FUIAction(FExecuteAction::CreateRaw(this,&FrdBPTools::SetPerInstanceHitProxies));
		FSlateIcon Icon15=FSlateIcon(FPluginStyle::GetStyleSetName(),"rdBPTools.ConvertToSMs","rdBPTools.ConvertToSMs");
		Section.AddEntry(FToolMenuEntry::InitMenuEntry("rdBPtools04",LOCTEXT("rdBPtools_LevelMenu15","Set PerInstance Editing"),LOCTEXT("rdBPtools_LevelMenu_Tooltip13","Sets the ISMC and HISMC flags to allow per-instance editing and enables instance edit mode (do not transform the prefab while editing instances)"),Icon15,Action15));

		FUIAction Action18=FUIAction(FExecuteAction::CreateRaw(this,&FrdBPTools::ClearPerInstanceHitProxies));
		FSlateIcon Icon18=FSlateIcon(FPluginStyle::GetStyleSetName(),"rdBPTools.ConvertToSMs","rdBPTools.ConvertToSMs");
		Section.AddEntry(FToolMenuEntry::InitMenuEntry("rdBPtools05",LOCTEXT("rdBPtools_LevelMenu16","Clear PerInstance Editing"),LOCTEXT("rdBPtools_LevelMenu_Tooltip13","Removes the ISMC and HISMC flags that allow per-instance editing and clears edit mode"),Icon18,Action18));

		FUIAction Action12=FUIAction(FExecuteAction::CreateRaw(this,&FrdBPTools::PlaceSelectionOnGround));
		FSlateIcon Icon12=FSlateIcon(FPluginStyle::GetStyleSetName(),"rdBPTools.PlaceOnGround","rdBPTools.PlaceOnGround");
		Section.AddEntry(FToolMenuEntry::InitMenuEntry("rdBPtools06",LOCTEXT("rdBPtools_LevelMenu12","Place on Ground"),LOCTEXT("rdBPtools_LevelMenu_Tooltip12","Place the objects in the selected blueprints on the ground"),Icon12,Action12));

		FSlateIcon Icon4=FSlateIcon(FPluginStyle::GetStyleSetName(),"rdBPTools.ChangeMobility","rdBPTools.ChangeMobility");
		Section.AddEntry(FToolMenuEntry::InitSubMenu("rdBPtools07",LOCTEXT("rdBPtools_LevelMenu4","Change Mobility"),LOCTEXT("rdBPtools_LevelMenu_Tooltip4","Change the Mobility of Components within the selected Blueprints"),
													FNewToolMenuDelegate::CreateRaw(this,&FrdBPTools::MobilitySubMenu),false,Icon4));

	} else if(numStaticMeshes>0) {

		FUIAction Action11=FUIAction(FExecuteAction::CreateRaw(this,&FrdBPTools::MoveSelectionToFoliage));
		FSlateIcon Icon11=FSlateIcon(FPluginStyle::GetStyleSetName(),"rdBPTools.MoveToFoliage","rdBPTools.MoveToFoliage");
		Section.AddEntry(FToolMenuEntry::InitMenuEntry("rdBPtools08",LOCTEXT("rdBPtools_LevelMenu11","Move To Foliage"),LOCTEXT("rdBPtools_LevelMenu_Tooltip11","Move the Selected StaticMesh Actors over to the Foliage System"),Icon11,Action11));
	}
}

//----------------------------------------------------------------------------------------------------------------
// MobilitySubMenu
//----------------------------------------------------------------------------------------------------------------
void FrdBPTools::MobilitySubMenu(UToolMenu* menu) {

	FToolMenuSection& Section=menu->AddSection("rdBPtoolsMobSec",LOCTEXT("rdBPtools_MobilityMenu_Label","Mobility"));

	FUIAction Action1=FUIAction(FExecuteAction::CreateRaw(this,&FrdBPTools::ChangeMobility_Static));
	FSlateIcon Icon1=FSlateIcon(FPluginStyle::GetStyleSetName(),"rdBPTools.MobilityStatic","rdBPTools.MobilityStatic");
	Section.AddEntry(FToolMenuEntry::InitMenuEntry("rdBPtoolsMob1",LOCTEXT("rdBPtools_MobMenu1","Static"),LOCTEXT("rdBPtools_MobMenu_Tooltip1","Components will be Static"),Icon1,Action1));

	FUIAction Action2=FUIAction(FExecuteAction::CreateRaw(this,&FrdBPTools::ChangeMobility_Stationary));
	FSlateIcon Icon2=FSlateIcon(FPluginStyle::GetStyleSetName(),"rdBPTools.MobilityStationary","rdBPTools.MobilityStationary");
	Section.AddEntry(FToolMenuEntry::InitMenuEntry("rdBPtoolsMob2",LOCTEXT("rdBPtools_MobMenu2","Stationary"),LOCTEXT("rdBPtools_MobMenu_Tooltip2","Components will be Stationary"),Icon2,Action2));

	FUIAction Action3=FUIAction(FExecuteAction::CreateRaw(this,&FrdBPTools::ChangeMobility_Movable));
	FSlateIcon Icon3=FSlateIcon(FPluginStyle::GetStyleSetName(),"rdBPTools.MobilityMovable","rdBPTools.MobilityMovable");
	Section.AddEntry(FToolMenuEntry::InitMenuEntry("rdBPtoolsMob3",LOCTEXT("rdBPtools_MobMenu3","Movable"),LOCTEXT("rdBPtools_MobMenu_Tooltip3","Components will be Movable"),Icon3,Action3));

}

void FrdBPTools::ChangeMobility_Static() {
	rdBPClassList.Add(MakeShared<rdBPclass>());
	rdBPclass& newBPclass=rdBPClassList[rdBPClassList.Num()-1].Get();
	newBPclass.rdBPoptions=NewObject<UrdBPtoolsOptions>(GetTransientPackage());
	newBPclass.rdBPoptions->AddToRoot();

	if(!FConfigCacheIni::LoadGlobalIniFile(newBPclass.configIni,TEXT("rdBPToolsConfig"))) {
#ifdef _INCLUDE_DEBUGGING_STUFF
		UE_LOG(LogTemp, Display,TEXT("rdBPTools: Failed to load rdBPTools config ini file"));
#endif
	}

	newBPclass.ChangeMobilityForSelectedActors(EComponentMobility::Static);
}

void FrdBPTools::ChangeMobility_Stationary() {
	rdBPClassList.Add(MakeShared<rdBPclass>());
	rdBPclass& newBPclass=rdBPClassList[rdBPClassList.Num()-1].Get();
	newBPclass.rdBPoptions=NewObject<UrdBPtoolsOptions>(GetTransientPackage());
	newBPclass.rdBPoptions->AddToRoot();

	if(!FConfigCacheIni::LoadGlobalIniFile(newBPclass.configIni,TEXT("rdBPToolsConfig"))) {
#ifdef _INCLUDE_DEBUGGING_STUFF
		UE_LOG(LogTemp, Display,TEXT("rdBPTools: Failed to load rdBPTools config ini file"));
#endif
	}

	newBPclass.ChangeMobilityForSelectedActors(EComponentMobility::Stationary);
}

void FrdBPTools::ChangeMobility_Movable() {
	rdBPClassList.Add(MakeShared<rdBPclass>());
	rdBPclass& newBPclass=rdBPClassList[rdBPClassList.Num()-1].Get();
	newBPclass.rdBPoptions=NewObject<UrdBPtoolsOptions>(GetTransientPackage());
	newBPclass.rdBPoptions->AddToRoot();

	if(!FConfigCacheIni::LoadGlobalIniFile(newBPclass.configIni,TEXT("rdBPToolsConfig"))) {
#ifdef _INCLUDE_DEBUGGING_STUFF
		UE_LOG(LogTemp, Display,TEXT("rdBPTools: Failed to load rdBPTools config ini file"));
#endif
	}

	newBPclass.ChangeMobilityForSelectedActors(EComponentMobility::Movable);
}

//----------------------------------------------------------------------------------------------------------------
// CreateSubMenu
//----------------------------------------------------------------------------------------------------------------
void FrdBPTools::CreateSubMenu(UToolMenu* menu) {

	FToolMenuSection& Section=menu->AddSection("rdBPtoolsCreateSec",LOCTEXT("rdBPtools_CreateMenu_Label","Creation"));

	FUIAction Action2=FUIAction(FExecuteAction::CreateRaw(this,&FrdBPTools::CreateFromLevel));
	FSlateIcon Icon2=FSlateIcon(FPluginStyle::GetStyleSetName(),"rdBPTools.CreateFromLevel","rdBPTools.CreateFromLevel");
	Section.AddEntry(FToolMenuEntry::InitMenuEntry("rdBPtools00",LOCTEXT("rdBPtools_LevelMenu2","Create From Selection..."),LOCTEXT("rdBPtools_LevelMenu_Tooltip2","Create a Blueprint from the selected Actors"),Icon2,Action2));

	if(numSelectedBPs>0) { // Only show if at least one blueprint actor is selected
/*
		FUIAction Action0=FUIAction(FExecuteAction::CreateRaw(this,&FrdBPTools::ReplaceSelectedBPs));
		FSlateIcon Icon0=FSlateIcon(FPluginStyle::GetStyleSetName(),"rdBPTools.ReplaceInLevel","rdBPTools.ReplaceInLevel");
		Section.AddEntry(FToolMenuEntry::InitMenuEntry("rdBPtools02",LOCTEXT("rdBPtools_LevelMenu0","Replace Selected Blueprints..."),LOCTEXT("rdBPtools_LevelMenu_Tooltip0","Replace the selected Blueprint Actors with their Actor Components"),Icon0,Action0));
*/
		//
		FUIAction Action5=FUIAction(FExecuteAction::CreateRaw(this,&FrdBPTools::UpdateSelectedPrefabsBPs));
		FSlateIcon Icon5=FSlateIcon(FPluginStyle::GetStyleSetName(),"rdBPTools.UpdateFromLevel","rdBPTools.UpdateFromLevel");
		Section.AddEntry(FToolMenuEntry::InitMenuEntry("rdBPtools15",LOCTEXT("rdBPtools_LevelMenu5","Update Prefab BPs"),LOCTEXT("rdBPtools_LevelMenu_Tooltip5","Updates the Blueprints with the edited items in the selected level instances"),Icon5,Action5));
	}

	FUIAction Action15=FUIAction(FExecuteAction::CreateRaw(this,&FrdBPTools::CreateSpawnActorFromSelection));
	FSlateIcon Icon15=FSlateIcon(FPluginStyle::GetStyleSetName(),"rdBPTools.UpdateFromLevel","rdBPTools.UpdateFromLevel");
	Section.AddEntry(FToolMenuEntry::InitMenuEntry("rdBPtools16",LOCTEXT("rdBPtools_LevelMenu15","Create rdSpawnStuffActor from Selection"),LOCTEXT("rdBPtools_LevelMenu_Tooltip15","Creates a new rdSpawnStuff Actor from the selection for Proximity Spawning"),Icon15,Action15));

#if ENGINE_MAJOR_VERSION>4
	if(ULevelInstanceSubsystem* LevelInstanceSubsystem=GEditor->GetEditorWorldContext().World()->GetSubsystem<ULevelInstanceSubsystem>()) {

		FUIAction Action9=FUIAction(FExecuteAction::CreateRaw(this,&FrdBPTools::CreateLevelInstance));
		FSlateIcon Icon9=FSlateIcon(FAppStyle::GetAppStyleSetName(),"ClassIcon.LevelInstance");
		Section.AddEntry(FToolMenuEntry::InitMenuEntry("rdBPtools06",LOCTEXT("rdBPtools_LevelMenu9","Create LevelInstance..."),LOCTEXT("rdBPtools_LevelMenu_Tooltip9","Create a LevelInstance from the Selected Blueprints"),Icon9,Action9));

		FUIAction Action10=FUIAction(FExecuteAction::CreateRaw(this,&FrdBPTools::CreatePackedLevelActor));
		FSlateIcon Icon10=FSlateIcon(FAppStyle::GetAppStyleSetName(),"ClassIcon.PackedLevelActor");
		Section.AddEntry(FToolMenuEntry::InitMenuEntry("rdBPtools07",LOCTEXT("rdBPtools_LevelMenu10","Create PackedLevelActor..."),LOCTEXT("rdBPtools_LevelMenu_Tooltip10","Create a PackedLevelActor from the StaticMeshes in the selected Blueprints"),Icon10,Action10));
	}
#endif
}

//----------------------------------------------------------------------------------------------------------------
// ConversionSubMenu
//----------------------------------------------------------------------------------------------------------------
void FrdBPTools::ConversionSubMenu(UToolMenu* menu) {

	FToolMenuSection& Section=menu->AddSection("rdBPtoolsConvSec",LOCTEXT("rdBPtools_ConversionMenu_Label","Conversion"));

	if(numSelectedBPs>0) { // Only show if at least one blueprint actor is selected

		FUIAction Action0=FUIAction(FExecuteAction::CreateRaw(this,&FrdBPTools::ReplaceSelectedBPs));
		FSlateIcon Icon0=FSlateIcon(FPluginStyle::GetStyleSetName(),"rdBPTools.ReplaceInLevel","rdBPTools.ReplaceInLevel");
		Section.AddEntry(FToolMenuEntry::InitMenuEntry("rdBPtools02",LOCTEXT("rdBPtools_LevelMenu0","Replace Selected Blueprints..."),LOCTEXT("rdBPtools_LevelMenu_Tooltip0","Replace the selected Blueprint Actors with their Actor Components"),Icon0,Action0));

		FUIAction Action1=FUIAction(FExecuteAction::CreateRaw(this,&FrdBPTools::CopyToLevel));
		FSlateIcon Icon1=FSlateIcon(FPluginStyle::GetStyleSetName(),"rdBPTools.CopyToLevel","rdBPTools.CopyToLevel");
		Section.AddEntry(FToolMenuEntry::InitMenuEntry("rdBPtools03",LOCTEXT("rdBPtools_LevelMenu1","Copy To Level..."),LOCTEXT("rdBPtools_LevelMenu_Tooltip1","Copy the Actor Components from the selected Blueprint Actors to the Level at the specified location/s"),Icon1,Action1));

		FUIAction Action13=FUIAction(FExecuteAction::CreateRaw(this,&FrdBPTools::ConvertSelectionToSMs));
		FSlateIcon Icon13=FSlateIcon(FPluginStyle::GetStyleSetName(),"rdBPTools.ConvertToSMs","rdBPTools.ConvertToSMs");
		Section.AddEntry(FToolMenuEntry::InitMenuEntry("rdBPtools09",LOCTEXT("rdBPtools_LevelMenu12","Convert selection to StaticMeshes"),LOCTEXT("rdBPtools_LevelMenu_Tooltip13","Converts the selected rdPrefabs Instances to StaticMeshes"),Icon13,Action13));

		FUIAction Action14=FUIAction(FExecuteAction::CreateRaw(this,&FrdBPTools::ConvertSelectionToISMs));
		FSlateIcon Icon14=FSlateIcon(FPluginStyle::GetStyleSetName(),"rdBPTools.ConvertToISMs","rdBPTools.ConvertToISMs");
		Section.AddEntry(FToolMenuEntry::InitMenuEntry("rdBPtools10",LOCTEXT("rdBPtools_LevelMenu13","Convert selection to Instances"),LOCTEXT("rdBPtools_LevelMenu_Tooltip13","Converts the selected rdPrefabs StaticMeshes to Instances"),Icon14,Action14));
	}

	FUIAction Action11=FUIAction(FExecuteAction::CreateRaw(this,&FrdBPTools::MoveSelectionToFoliage));
	FSlateIcon Icon11=FSlateIcon(FPluginStyle::GetStyleSetName(),"rdBPTools.MoveToFoliage","rdBPTools.MoveToFoliage");
	Section.AddEntry(FToolMenuEntry::InitMenuEntry("rdBPtools04",LOCTEXT("rdBPtools_LevelMenu11","Move Actors to Foliage"),LOCTEXT("rdBPtools_LevelMenu_Tooltip11","Move the selected Actors and Blueprints Instances over to the Foliage System"),Icon11,Action11));
}

//----------------------------------------------------------------------------------------------------------------
// RandomSubMenu
//----------------------------------------------------------------------------------------------------------------
void FrdBPTools::RandomSubMenu(UToolMenu* menu) {

	FToolMenuSection& Section=menu->AddSection("rdBPtoolsRandSec",LOCTEXT("rdBPtools_RandomMenu_Label","Randomization"));

	// Random Settings
	FUIAction Action3=FUIAction(FExecuteAction::CreateRaw(this,&FrdBPTools::ChangeRandomActorSettings));
	FSlateIcon Icon3=FSlateIcon(FPluginStyle::GetStyleSetName(),"rdBPTools.ChangeRandomSettings","rdBPTools.ChangeRandomSettings");
	Section.AddEntry(FToolMenuEntry::InitMenuEntry("rdBPtools12",LOCTEXT("rdBPtools_LevelMenu3","Randomize Settings..."),LOCTEXT("rdBPtools_LevelMenu_Tooltip3","Change the Randomize Settings used for the selected actors (for when used with the Container Blueprint)"),Icon3,Action3));

	FUIAction Action7=FUIAction(FExecuteAction::CreateRaw(this,&FrdBPTools::SetRelyOnActors));
	FSlateIcon Icon7=FSlateIcon(FPluginStyle::GetStyleSetName(),"rdBPTools.SetRelyOnActors","rdBPTools.SetRelyOnActors");
	Section.AddEntry(FToolMenuEntry::InitMenuEntry("rdBPtools13",LOCTEXT("rdBPtools_LevelMenu7","Set Rely On Actors"),LOCTEXT("rdBPtools_LevelMenu1_Tooltip7","Set the 'Rely On Actor' to point to the previous actor in the selection"),Icon7,Action7));

	FUIAction Action6=FUIAction(FExecuteAction::CreateRaw(this,&FrdBPTools::RemoveRandomActorTags));
	FSlateIcon Icon6=FSlateIcon(FPluginStyle::GetStyleSetName(),"rdBPTools.RemoveRandomSettings","rdBPTools.RemoveRandomSettings");
	Section.AddEntry(FToolMenuEntry::InitMenuEntry("rdBPtools14",LOCTEXT("rdBPtools_LevelMenu6","Remove Randomize Settings..."),LOCTEXT("rdBPtools_LevelMenu_Tooltip6","Remove the Randomize Settings set in the selected Actors"),Icon6,Action6));
}

//----------------------------------------------------------------------------------------------------------------
// HarvestInstancesSubMenu
//----------------------------------------------------------------------------------------------------------------
void FrdBPTools::HarvestInstancesSubMenu(UToolMenu* menu) {

	FToolMenuSection& Section=menu->AddSection("rdBPtoolsHarvSec",LOCTEXT("rdBPtools_HarvestMenu_Label","Harvest"));
	
	FUIAction Action1=FUIAction(FExecuteAction::CreateRaw(this,&FrdBPTools::HarvestInstancesToLevel));
	FSlateIcon Icon1=FSlateIcon(FPluginStyle::GetStyleSetName(),"rdBPTools.HarvestInstancesToLevel","rdBPTools.HarvestInstancesToLevel");
	Section.AddEntry(FToolMenuEntry::InitMenuEntry("rdBPtoolss1",LOCTEXT("rdBPtools_HarvestInstancesMenu1","to Level"),LOCTEXT("rdBPtools_HarvestInstancesMenu_Tooltip1","Convert any StaticMesh Instances in the selected assets to StaticMeshes in the Level"),Icon1,Action1));
	
	FUIAction Action2=FUIAction(FExecuteAction::CreateRaw(this,&FrdBPTools::HarvestInstancesToBP));
	FSlateIcon Icon2=FSlateIcon(FPluginStyle::GetStyleSetName(),"rdBPTools.HarvestInstancesToBP","rdBPTools.HarvestInstancesToBP");
	Section.AddEntry(FToolMenuEntry::InitMenuEntry("rdBPtoolss2",LOCTEXT("rdBPtools_HarvestInstancesMenu2","to Blueprint"),LOCTEXT("rdBPtools_HarvestInstancesMenu_Tooltip2","Convert any StaticMesh Instances in the selected assets to a Blueprint"),Icon2,Action2));
	
	FUIAction Action3=FUIAction(FExecuteAction::CreateRaw(this,&FrdBPTools::HarvestInstancesToSpawnActor));
	FSlateIcon Icon3=FSlateIcon(FPluginStyle::GetStyleSetName(),"rdBPTools.HarvestInstancesToBP","rdBPTools.HarvestInstancesToBP");
	Section.AddEntry(FToolMenuEntry::InitMenuEntry("rdBPtoolss3",LOCTEXT("rdBPtools_HarvestInstancesMenu3","to Baked rdSpawnStuff Actor"),LOCTEXT("rdBPtools_HarvestInstancesMenu_Tooltip2","Convert any StaticMesh Instances in the selected assets to a Baked rdSpawnActor"),Icon3,Action3));
}

//----------------------------------------------------------------------------------------------------------------
// HarvestInstancesToLevel
//----------------------------------------------------------------------------------------------------------------
void FrdBPTools::HarvestInstancesToLevel() {

	rdBPClassList.Add(MakeShared<rdBPclass>());
	rdBPclass& newBPclass=rdBPClassList[rdBPClassList.Num()-1].Get();
	newBPclass.rdBPoptions=NewObject<UrdBPtoolsOptions>(GetTransientPackage());
	newBPclass.rdBPoptions->AddToRoot();

	if(!FConfigCacheIni::LoadGlobalIniFile(newBPclass.configIni,TEXT("rdBPToolsConfig"))) {
#ifdef _INCLUDE_DEBUGGING_STUFF
		UE_LOG(LogTemp, Display,TEXT("rdBPTools: Failed to load rdBPTools config ini file"));
#endif
	}

	newBPclass.HarvestInstancesFromSelectedActors(false,true);
}

//----------------------------------------------------------------------------------------------------------------
// HarvestInstancesToBP
//----------------------------------------------------------------------------------------------------------------
void FrdBPTools::HarvestInstancesToBP() {

	rdBPClassList.Add(MakeShared<rdBPclass>());
	rdBPclass& newBPclass=rdBPClassList[rdBPClassList.Num()-1].Get();
	newBPclass.rdBPoptions=NewObject<UrdBPtoolsOptions>(GetTransientPackage());
	newBPclass.rdBPoptions->AddToRoot();

	if(!FConfigCacheIni::LoadGlobalIniFile(newBPclass.configIni,TEXT("rdBPToolsConfig"))) {
#ifdef _INCLUDE_DEBUGGING_STUFF
		UE_LOG(LogTemp, Display,TEXT("rdBPTools: Failed to load rdBPTools config ini file"));
#endif
	}

	newBPclass.HarvestInstancesFromSelectedActors(true,true);
}

//----------------------------------------------------------------------------------------------------------------
// HarvestInstancesToSpawnActor
//----------------------------------------------------------------------------------------------------------------
void FrdBPTools::HarvestInstancesToSpawnActor() {

	rdBPClassList.Add(MakeShared<rdBPclass>());
	rdBPclass& newBPclass=rdBPClassList[rdBPClassList.Num()-1].Get();
	newBPclass.rdBPoptions=NewObject<UrdBPtoolsOptions>(GetTransientPackage());
	newBPclass.rdBPoptions->AddToRoot();

	if(!FConfigCacheIni::LoadGlobalIniFile(newBPclass.configIni,TEXT("rdBPToolsConfig"))) {
#ifdef _INCLUDE_DEBUGGING_STUFF
		UE_LOG(LogTemp, Display,TEXT("rdBPTools: Failed to load rdBPTools config ini file"));
#endif
	}

	newBPclass.HarvestInstancesFromSelectedActorsToSpawnActor(true,true);
}

//----------------------------------------------------------------------------------------------------------------
// BlueprintSubMenu
//----------------------------------------------------------------------------------------------------------------
void FrdBPTools::BlueprintSubMenu(UToolMenu* menu) {

	FContentBrowserModule& ContentBrowserModule=FModuleManager::Get().LoadModuleChecked<FContentBrowserModule>(TEXT("ContentBrowser"));

	FAssetPickerConfig Config;
#if ENGINE_MAJOR_VERSION>4 && ENGINE_MINOR_VERSION>0
	Config.Filter.ClassPaths.Add(UBlueprint::StaticClass()->GetClassPathName());
#else
	Config.Filter.ClassNames.Add(UBlueprint::StaticClass()->GetFName());
#endif
	Config.InitialAssetViewType=EAssetViewType::List;
	Config.OnAssetSelected=FOnAssetSelected::CreateRaw(this,&FrdBPTools::OnBPSelected);
	Config.bAllowDragging=false;
	Config.SaveSettingsName=FString(TEXT("rdBPtoolsSelectBP"));
	Config.SelectionMode=ESelectionMode::Single;

	TSharedRef<SWidget> widget=	SNew(SBox)
								.WidthOverride(300.f)
								.HeightOverride(300.f)
								[
									ContentBrowserModule.Get().CreateAssetPicker(Config)
								];
	{
		FToolMenuSection& Section=menu->AddSection("Browse",LOCTEXT("BrowseHeader","Browse"));
		Section.AddEntry(FToolMenuEntry::InitWidget("PickClassWidget",widget,FText::GetEmpty()));
	}
}

//----------------------------------------------------------------------------------------------------------------
// OnBPSelected
//----------------------------------------------------------------------------------------------------------------
void FrdBPTools::OnBPSelected(const struct FAssetData& AssetData) {

	UBlueprint* selectedBP=Cast<UBlueprint>(AssetData.GetAsset());
	if(selectedBP) {
		
		rdBPClassList.Add(MakeShared<rdBPclass>());
		rdBPclass& newBPclass=rdBPClassList[rdBPClassList.Num()-1].Get();
		newBPclass.rdBPoptions=NewObject<UrdBPtoolsOptions>(GetTransientPackage());
		newBPclass.rdBPoptions->AddToRoot();
		newBPclass.rdBPoptions->bp=selectedBP;
		newBPclass.bp=selectedBP;

		//newBPclass.UpdatePrefabBPfromActor();
	}
}

//----------------------------------------------------------------------------------------------------------------
// MoveSelectionToFoliage
//----------------------------------------------------------------------------------------------------------------
void FrdBPTools::MoveSelectionToFoliage() {

	rdBPClassList.Add(MakeShared<rdBPclass>());
	rdBPclass& newBPclass=rdBPClassList[rdBPClassList.Num()-1].Get();
	newBPclass.rdBPoptions=NewObject<UrdBPtoolsOptions>(GetTransientPackage());
	newBPclass.rdBPoptions->AddToRoot();

	if(!FConfigCacheIni::LoadGlobalIniFile(newBPclass.configIni,TEXT("rdBPToolsConfig"))) {
#ifdef _INCLUDE_DEBUGGING_STUFF
		UE_LOG(LogTemp, Display,TEXT("rdBPTools: Failed to load rdBPTools config ini file"));
#endif
	}

	newBPclass.rdBPoptions->showFoliageTypeSettings=FSlateApplication::Get().GetModifierKeys().IsShiftDown();

	newBPclass.MoveToFoliageForSelectedActors();
}

//----------------------------------------------------------------------------------------------------------------
// PlaceSelectionOnGround
//----------------------------------------------------------------------------------------------------------------
void FrdBPTools::PlaceSelectionOnGround() {

	rdBPClassList.Add(MakeShared<rdBPclass>());
	rdBPclass& newBPclass=rdBPClassList[rdBPClassList.Num()-1].Get();
	newBPclass.rdBPoptions=NewObject<UrdBPtoolsOptions>(GetTransientPackage());
	newBPclass.rdBPoptions->AddToRoot();

	if(!FConfigCacheIni::LoadGlobalIniFile(newBPclass.configIni,TEXT("rdBPToolsConfig"))) {
#ifdef _INCLUDE_DEBUGGING_STUFF
		UE_LOG(LogTemp, Display,TEXT("rdBPTools: Failed to load rdBPTools config ini file"));
#endif
	}

	newBPclass.PlaceOnGroundForSelectedActors();
}

//----------------------------------------------------------------------------------------------------------------
// ConvertSelectionToSMs
//----------------------------------------------------------------------------------------------------------------
void FrdBPTools::ConvertSelectionToSMs() {

	rdBPClassList.Add(MakeShared<rdBPclass>());
	rdBPclass& newBPclass=rdBPClassList[rdBPClassList.Num()-1].Get();
	newBPclass.rdBPoptions=NewObject<UrdBPtoolsOptions>(GetTransientPackage());
	newBPclass.rdBPoptions->AddToRoot();

	if(!FConfigCacheIni::LoadGlobalIniFile(newBPclass.configIni,TEXT("rdBPToolsConfig"))) {
#ifdef _INCLUDE_DEBUGGING_STUFF
		UE_LOG(LogTemp, Display,TEXT("rdBPTools: Failed to load rdBPTools config ini file"));
#endif
	}

	newBPclass.ConvertSelectedActorstoSMs();
}

//----------------------------------------------------------------------------------------------------------------
// ConvertSelectionToISMs
//----------------------------------------------------------------------------------------------------------------
void FrdBPTools::ConvertSelectionToISMs() {

	rdBPClassList.Add(MakeShared<rdBPclass>());
	rdBPclass& newBPclass=rdBPClassList[rdBPClassList.Num()-1].Get();
	newBPclass.rdBPoptions=NewObject<UrdBPtoolsOptions>(GetTransientPackage());
	newBPclass.rdBPoptions->AddToRoot();

	if(!FConfigCacheIni::LoadGlobalIniFile(newBPclass.configIni,TEXT("rdBPToolsConfig"))) {
#ifdef _INCLUDE_DEBUGGING_STUFF
		UE_LOG(LogTemp, Display,TEXT("rdBPTools: Failed to load rdBPTools config ini file"));
#endif
	}

	newBPclass.ConvertSelectedActorstoISMs();
}

//----------------------------------------------------------------------------------------------------------------
// SetPerInstanceHitProxies
//----------------------------------------------------------------------------------------------------------------
void FrdBPTools::SetPerInstanceHitProxies() {

	rdBPClassList.Add(MakeShared<rdBPclass>());
	rdBPclass& newBPclass=rdBPClassList[rdBPClassList.Num()-1].Get();
	newBPclass.rdBPoptions=NewObject<UrdBPtoolsOptions>(GetTransientPackage());
	newBPclass.rdBPoptions->AddToRoot();

	if(!FConfigCacheIni::LoadGlobalIniFile(newBPclass.configIni,TEXT("rdBPToolsConfig"))) {
#ifdef _INCLUDE_DEBUGGING_STUFF
		UE_LOG(LogTemp, Display,TEXT("rdBPTools: Failed to load rdBPTools config ini file"));
#endif
	}

	FSelectionIterator it(*GEditor->GetSelectedActors());
	currentEditInstanceActor=Cast<AActor>(*it);

	newBPclass.SetPerInstanceHitProxies(true);
}

//----------------------------------------------------------------------------------------------------------------
// ClearPerInstanceHitProxies
//----------------------------------------------------------------------------------------------------------------
void FrdBPTools::ClearPerInstanceHitProxies() {

	rdBPClassList.Add(MakeShared<rdBPclass>());
	rdBPclass& newBPclass=rdBPClassList[rdBPClassList.Num()-1].Get();
	newBPclass.rdBPoptions=NewObject<UrdBPtoolsOptions>(GetTransientPackage());
	newBPclass.rdBPoptions->AddToRoot();

	if(!FConfigCacheIni::LoadGlobalIniFile(newBPclass.configIni,TEXT("rdBPToolsConfig"))) {
#ifdef _INCLUDE_DEBUGGING_STUFF
		UE_LOG(LogTemp, Display,TEXT("rdBPTools: Failed to load rdBPTools config ini file"));
#endif
	}

	newBPclass.SetPerInstanceHitProxies(false);
	currentEditInstanceActor=nullptr;
}

//----------------------------------------------------------------------------------------------------------------
#undef LOCTEXT_NAMESPACE

