//
// rdBP_Stats.cpp
//
// Copyright (c) 2022 Recourse Design ltd. All rights reserved.
//
// Creation Date: 29th August 2022
// Last Modified: 26th June 2023
//
#include "../Public/rdBPtools.h"
#include "Editor/MainFrame/Public/Interfaces/IMainFrameModule.h"
#include "Components/StaticMeshComponent.h"
#include "Engine/Selection.h"
#include "Dialogs/Dialogs.h"
#include "Misc/MessageDialog.h"
#include "rdBPtoolsOptions.h"

#define LOCTEXT_NAMESPACE "FrdBPtoolsModule"

//----------------------------------------------------------------------------------------------------------------
// CalcStatsForComp
//----------------------------------------------------------------------------------------------------------------
void rdBPclass::CalcStatsForComp(UActorComponent* comp,int32& numBPs,int32& numActors,int32& numComponents,int32& numSubBlueprints,int32& numStaticMeshInstances,int32& numSolidStaticMeshes,TArray<UStaticMesh*>& lstStaticMeshes) {

	if(comp->IsA(UChildActorComponent::StaticClass())) {

		numComponents++;
		UChildActorComponent* childActorComp=Cast<UChildActorComponent>(comp);
		if(childActorComp) {
			AActor* actor=childActorComp->GetChildActor(); 
			if(!actor) actor=childActorComp->GetChildActorTemplate();
			if(actor) {
				CalcStatsForActor(actor,numBPs,numActors,numComponents,numSubBlueprints,numStaticMeshInstances,numSolidStaticMeshes,lstStaticMeshes);
			}
		}

	} else if(comp->IsA(UStaticMeshComponent::StaticClass())) {

		numComponents++;
		UStaticMeshComponent* staticMesh=Cast<UStaticMeshComponent>(comp);
		if(staticMesh) {
			numSolidStaticMeshes++;
		}

	} else if(comp->IsA(USceneComponent::StaticClass())) {

		USceneComponent* scene=Cast<USceneComponent>(comp);
		if(scene) {
		}
	}
}

//----------------------------------------------------------------------------------------------------------------
// CalcStatsForBlueprint
//----------------------------------------------------------------------------------------------------------------
void rdBPclass::CalcStatsForBlueprint(UBlueprint* blueprint,int32& numBPs,int32& numActors,int32& numComponents,int32& numSubBlueprints,int32& numStaticMeshInstances,int32& numSolidStaticMeshes,TArray<UStaticMesh*>& lstStaticMeshes) {

	numBPs++;

	AActor* actor=(AActor*)blueprint->GeneratedClass->GetDefaultObject();
	if(actor) {
		CalcStatsForActor(actor,numBPs,numActors,numComponents,numSubBlueprints,numStaticMeshInstances,numSolidStaticMeshes,lstStaticMeshes);
	
	} else {

		TArray<UActorComponent*> comps;
		GetBlueprintClassComponents(blueprint,comps);

		for(UActorComponent* comp:comps) {
			CalcStatsForComp(comp,numBPs,numActors,numComponents,numSubBlueprints,numStaticMeshInstances,numSolidStaticMeshes,lstStaticMeshes);
		}
	}
}

//----------------------------------------------------------------------------------------------------------------
// CalcStatsForActor
//----------------------------------------------------------------------------------------------------------------
void rdBPclass::CalcStatsForActor(AActor* actor,int32& numBPs,int32& numActors,int32& numComponents,int32& numSubBlueprints,int32& numStaticMeshInstances,int32& numSolidStaticMeshes,TArray<UStaticMesh*>& lstStaticMeshes) {
	
	numActors++;

	UBlueprintGeneratedClass* bpClass=Cast<UBlueprintGeneratedClass>(actor->GetClass());
	if(bpClass) {
		TArray<UActorComponent*> comps;
		GetBlueprintClassComponents(Cast<UBlueprint>(bpClass->ClassGeneratedBy),comps);
		for(auto comp:comps) {
			CalcStatsForComp(comp,numBPs,numActors,numComponents,numSubBlueprints,numStaticMeshInstances,numSolidStaticMeshes,lstStaticMeshes);
		}
	}
/* //TODO:
	ArdActor* rdActor=Cast<ArdActor>(actor);
	if(rdActor) {
		
		numSubBlueprints++;

		for(auto it:rdActor->InstanceData) {
			UStaticMesh* sm=it.Key;
			lstStaticMeshes.AddUnique(sm);
			FrdInstanceSettingsArray& i=rdActor->InstanceData[sm];
			for(FrdInstanceFastSettings& j:i.settingsFast) {
				if(j.bVisible) {
					numStaticMeshInstances++;
				}
			}
			for(FrdInstanceRandomSettings& j:i.settingsRandom) {
				if(j.bVisible) {
					numStaticMeshInstances++;
				}
			}
		}
	}
*/
}

//----------------------------------------------------------------------------------------------------------------
// DisplayStatsForSelectedFiles
//----------------------------------------------------------------------------------------------------------------
void rdBPclass::DisplayStatsForSelectedFiles() {

	FString stats=TEXT("Empty");
	int32 numBPs=0,numActors=0,numComponents=0,numSubBlueprints=0,numStaticMeshInstances=0,numSolidStaticMeshes=0;
	TArray<UStaticMesh*> lstStaticMeshes;

	for(auto asset:selectedBPs) {

		UPackage* package=rdGetPackage(asset.PackageName.ToString());
		if(package) {
			bp=FindObject<UBlueprint>(package,*(asset.AssetName.ToString()),true);
			if(bp) {
				CalcStatsForBlueprint(bp,numBPs,numActors,numComponents,numSubBlueprints,numStaticMeshInstances,numSolidStaticMeshes,lstStaticMeshes);
			}
		}
	}	
	
	// show dialog
	stats=FString::Printf(TEXT("\nBlueprint Stats\n\nNumber Blueprints: %d\nNumber SubBlueprints: %d\nNumber Actors: %d\nNumber Components: %d\n\nNumber Unique Static Meshes: %d\nNumber Static Mesh Instances: %d\nNumber Solid Static Meshes: %d"),numBPs,numSubBlueprints,numActors,numComponents,lstStaticMeshes.Num(),numStaticMeshInstances,numSolidStaticMeshes);
	SGenericDialogWidget::OpenDialog(LOCTEXT("BlueprintStatistics","Blueprint Statistics"),SNew(STextBlock).Text(FText::FromString(*stats)),SGenericDialogWidget::FArguments(),true);
}

//----------------------------------------------------------------------------------------------------------------
// DisplayStatsForSelectedAssets
//----------------------------------------------------------------------------------------------------------------
void rdBPclass::DisplayStatsForSelectedAssets() {

	FString stats=TEXT("Empty");
	int32 numBPs=0,numActors=0,numComponents=0,numSubBlueprints=0,numStaticMeshInstances=0,numSolidStaticMeshes=0;
	TArray<UStaticMesh*> lstStaticMeshes;

	for(FSelectionIterator it(*GEditor->GetSelectedActors());it;++it) {
		UBlueprintGeneratedClass* bpClass=Cast<UBlueprintGeneratedClass>((*it)->GetClass()); // Blueprints
		if(bpClass) {
			bpActor=Cast<AActor>(*it);
			if(bpActor) {
				CalcStatsForActor(bpActor,numBPs,numActors,numComponents,numSubBlueprints,numStaticMeshInstances,numSolidStaticMeshes,lstStaticMeshes);
			}
		}
	}

	// show dialog
	stats=FString::Printf(TEXT("\nBlueprint Stats\n\nNumber Blueprints: %d\nNumber SubBlueprints: %d\nNumber Actors: %d\nNumber Components: %d\n\nNumber Unique Static Meshes: %d\nNumber Static Mesh Instances: %d\nNumber Solid Static Meshes: %d"),numBPs,numSubBlueprints,numActors,numComponents,lstStaticMeshes.Num(),numStaticMeshInstances,numSolidStaticMeshes);
	SGenericDialogWidget::OpenDialog(LOCTEXT("BlueprintStatistics","Blueprint Statistics"),SNew(STextBlock).Text(FText::FromString(*stats)),SGenericDialogWidget::FArguments(),true);
}

//----------------------------------------------------------------------------------------------------------------
// DisplayStatsForSelectedComps
//----------------------------------------------------------------------------------------------------------------
void rdBPclass::DisplayStatsForSelectedComps() {

	FString stats=TEXT("Empty");
	int32 numBPs=0,numActors=0,numComponents=0,numSubBlueprints=0,numStaticMeshInstances=0,numSolidStaticMeshes=0;
	TArray<UStaticMesh*> lstStaticMeshes;

	TArray<USceneComponent*> comps;
	int32 numSel=GetSelectedComponentsFromBlueprintEditor(comps);

	for(auto comp:comps) {
		CalcStatsForComp(comp,numBPs,numActors,numComponents,numSubBlueprints,numStaticMeshInstances,numSolidStaticMeshes,lstStaticMeshes);
	}

	// show dialog
	stats=FString::Printf(TEXT("\nBlueprint Stats\n\nNumber Blueprints: %d\nNumber SubBlueprints: %d\nNumber Actors: %d\nNumber Components: %d\n\nNumber Unique Static Meshes: %d\nNumber Static Mesh Instances: %d\nNumber Solid Static Meshes: %d"),numBPs,numSubBlueprints,numActors,numComponents,lstStaticMeshes.Num(),numStaticMeshInstances,numSolidStaticMeshes);
	SGenericDialogWidget::OpenDialog(LOCTEXT("BlueprintStatistics","Blueprint Statistics"),SNew(STextBlock).Text(FText::FromString(*stats)),SGenericDialogWidget::FArguments(),true);
}

//----------------------------------------------------------------------------------------------------------------

#undef LOCTEXT_NAMESPACE
