// Copyright Epic Games, Inc. All Rights Reserved.

#include "AndroidEOSVoiceChat.h"

#if WITH_EOSVOICECHAT

#include "AndroidEOSVoiceChatUser.h"

FAndroidEOSVoiceChat::FAndroidEOSVoiceChat(IEOSSDKManager& InSDKManager, const IEOSPlatformHandlePtr& InPlatformHandle)
	: FEOSVoiceChat(InSDKManager, InPlatformHandle)
{
}

IVoiceChatUser* FAndroidEOSVoiceChat::CreateUser()
{
	const FEOSVoiceChatUserRef& User = VoiceChatUsers.Emplace_GetRef(MakeShared<FAndroidEOSVoiceChatUser, ESPMode::ThreadSafe>(*this));
	return &User.Get();
}

#endif // WITH_EOSVOICECHAT