// Copyright Epic Games, Inc. All Rights Reserved.

#include "VCamCoreEditorModule.h"

#include "Customizations/OutputProvider/ConnectionRemapCustomization_StateSwitcher.h"
#include "Customizations/OutputProvider/ConnectionRemapCustomization_VCamWidget.h"
#include "Customizations/OutputProvider/OutputProviderLayoutCustomization.h"
#include "Customizations/StateSwitcher/VCamStateSwitcherWidgetCustomization.h"
#include "Customizations/StateSwitcher/WidgetConnectionConfigTypeCustomization.h"
#include "Customizations/TargetSettings/ConnectionTargetSettingsTypeCustomization.h"
#include "Customizations/TargetSettings/InsideVCamConnectionContextFinder.h"
#include "Customizations/TargetSettings/ListOfContextFinders.h"
#include "Customizations/TargetSettings/StateSwitcherContextFinder.h"
#include "Customizations/VCamBaseActorCustomization.h"
#include "Customizations/VCamInputProfileCustomization.h"
#include "Customizations/VCamViewportLockerTypeCustomization.h"
#include "Customizations/WidgetReference/ChildWidgetReferenceCustomization.h"
#include "Customizations/WidgetReference/VCamChildWidgetReferenceCustomization.h"
#include "EditorOnlyVCamModifier.h"
#include "EditorOnlyVCamModifierBlueprint.h"
#include "Input/VCamInputDeviceConfig.h"
#include "LogVCamEditor.h"
#include "Modifier/VCamModifier.h"
#include "Output/VCamOutputProviderBase.h"
#include "UI/Switcher/WidgetConnectionConfig.h"
#include "UI/Switcher/VCamStateSwitcherWidget.h"
#include "UI/VCamWidget.h"
#include "Util/WidgetReference.h"
#include "VCamBaseActor.h"
#include "VCamComponent.h"

#include "AssetToolsModule.h"
#include "ConcertTransactionEvents.h"
#include "IConcertSyncClient.h"
#include "IConcertSyncClientModule.h"
#include "KismetCompilerModule.h"
#include "Kismet2/KismetEditorUtilities.h"
#include "Modules/ModuleInterface.h"
#include "Modules/ModuleManager.h"
#include "PropertyEditorModule.h"
#include "Compilation/CompilationExtensionManager.h"
#include "Customizations/DeviceID/VCamInputDeviceIDTypeCustomization.h"

#define LOCTEXT_NAMESPACE "FVCamCoreEditorModule"

namespace UE::VCamCoreEditor
{
	TSharedPtr<IConnectionRemapCustomization> FVCamCoreEditorModule::CreateConnectionRemapCustomization(TSubclassOf<UVCamWidget> Class) const
	{
		for (UClass* const CurrentClass = Class; CurrentClass != UUserWidget::StaticClass(); Class = Class->GetSuperClass())
		{
			const FGetConnectionRemappingCustomization* FactoryDelegate = ConnectionRemappingCustomizationFactories.Find(Class);
			if (FactoryDelegate)
			{
				return FactoryDelegate->Execute();
			}
		}
		return nullptr;
	}

	void FVCamCoreEditorModule::StartupModule()
	{
		RegisterAutoGeneratedDefaultEvents();
		RegisterCustomizations();
		RegisterDefaultConnectionRemappingCustomizations();

		CompilationExtensionManager = MakeShared<FCompilationExtensionManager>();
		CompilationExtensionManager->Init();

		// Use a custom UBlueprint, so we can get the editor to show editor-only functions in it.
		IKismetCompilerInterface& KismetCompilerModule = FModuleManager::LoadModuleChecked<IKismetCompilerInterface>("KismetCompiler");
		KismetCompilerModule.OverrideBPTypeForClass(UEditorOnlyVCamModifier::StaticClass(), UEditorOnlyVCamModifierBlueprint::StaticClass());
		
		FCoreDelegates::OnPostEngineInit.AddRaw(this, &FVCamCoreEditorModule::RegisterMultiUserFilters);
	}
	
	void FVCamCoreEditorModule::ShutdownModule()
	{
		CompilationExtensionManager.Reset();
		
		FKismetEditorUtilities::UnregisterAutoBlueprintNodeCreation(this);
		if (UObjectInitialized() && !IsEngineExitRequested())
		{
			UnregisterCustomizations();
		}
	}

	void FVCamCoreEditorModule::RegisterConnectionRemapCustomization(TSubclassOf<UVCamWidget> Class, FGetConnectionRemappingCustomization GetterDelegate)
	{
		if (!Class)
		{
			return;
		}

		if (Class->GetClass()->IsChildOf(UBlueprintGeneratedClass::StaticClass()))
		{
			UE_LOG(LogVCamEditor,
				Error,
				TEXT("RegisterConnectionTargetRemappingCustomizer is designed for native C++ base classes."
					"Blueprints are untested and will likely break due to recompilation and reinstancing."
					"'%s' is will not be registered!"
					),
				*Class->GetName()
				);
			return;
		}
		
		if (ConnectionRemappingCustomizationFactories.Contains(Class))
		{
			UE_LOG(LogVCamEditor, Error, TEXT("Class '%s' is already registered!"), *Class->GetName());
			return;
		}

		ConnectionRemappingCustomizationFactories.Add(Class, MoveTemp(GetterDelegate));
	}
	
	void FVCamCoreEditorModule::UnregisterConnectionRemapCustomization(TSubclassOf<UVCamWidget> Class)
	{
		const bool bIsInvalidClass = Class == UVCamWidget::StaticClass()
			|| Class == UVCamStateSwitcherWidget::StaticClass();
		if (bIsInvalidClass)
		{
			UE_LOG(LogVCamEditor, Error, TEXT("Class '%s' cannot be unregistered!"), Class ? *Class->GetName() : TEXT("null"));
			return;
		}

		// Any alive instances will be cleared when the details panel redraws
		ConnectionRemappingCustomizationFactories.Remove(Class);
	}

	uint32 FVCamCoreEditorModule::GetAdvancedAssetCategoryForVCam() const
	{
		IAssetTools& AssetTools = FModuleManager::LoadModuleChecked<FAssetToolsModule>("AssetTools").Get();
		return AssetTools.RegisterAdvancedAssetCategory("VirtualCamera", LOCTEXT("AssetCategoryName", "VCam"));
	}

	void FVCamCoreEditorModule::RegisterAutoGeneratedDefaultEvents()
	{
		FKismetEditorUtilities::RegisterAutoGeneratedDefaultEvent(this, UVCamBlueprintModifier::StaticClass(), "OnInitialize");
		FKismetEditorUtilities::RegisterAutoGeneratedDefaultEvent(this, UVCamBlueprintModifier::StaticClass(), "OnDeinitialize");
		FKismetEditorUtilities::RegisterAutoGeneratedDefaultEvent(this, UVCamBlueprintModifier::StaticClass(), "OnApply");
	}
	
	void FVCamCoreEditorModule::RegisterCustomizations()
	{
		FPropertyEditorModule& PropertyModule = FModuleManager::GetModuleChecked<FPropertyEditorModule>("PropertyEditor");

		PropertyModule.RegisterCustomPropertyTypeLayout(
			FVCamInputProfile::StaticStruct()->GetFName(),
			FOnGetPropertyTypeCustomizationInstance::CreateStatic(&FVCamInputProfileCustomization::MakeInstance)
		);
		PropertyModule.RegisterCustomPropertyTypeLayout(
			FVCamConnectionTargetSettings::StaticStruct()->GetFName(),
			FOnGetPropertyTypeCustomizationInstance::CreateLambda([]()
			{
				using namespace ConnectionTargetContextFinding;
				TArray<TSharedRef<IContextFinderForConnectionTargetSettings>> ContextFinders
				{
					MakeShared<FInsideVCamConnectionContextFinder>(),
					MakeShared<FStateSwitcherContextFinder>()
				};
				return FConnectionTargetSettingsTypeCustomization::MakeInstance(
					MakeShared<FListOfContextFinders>(MoveTemp(ContextFinders))
					);
			})
		);
		PropertyModule.RegisterCustomPropertyTypeLayout(
			FWidgetConnectionConfig::StaticStruct()->GetFName(), 
			FOnGetPropertyTypeCustomizationInstance::CreateStatic(&FWidgetConnectionConfigTypeCustomization::MakeInstance)
		);
		PropertyModule.RegisterCustomPropertyTypeLayout(
			FVCamViewportLocker::StaticStruct()->GetFName(), 
			FOnGetPropertyTypeCustomizationInstance::CreateStatic(&FVCamViewportLockerTypeCustomization::MakeInstance)
			);
		PropertyModule.RegisterCustomPropertyTypeLayout(
			FChildWidgetReference::StaticStruct()->GetFName(),
			FOnGetPropertyTypeCustomizationInstance::CreateStatic(&FChildWidgetReferenceCustomization::MakeInstance)
			);
		PropertyModule.RegisterCustomPropertyTypeLayout(
			FVCamChildWidgetReference::StaticStruct()->GetFName(),
			FOnGetPropertyTypeCustomizationInstance::CreateStatic(&FVCamChildWidgetReferenceCustomization::MakeInstance)
			);
		PropertyModule.RegisterCustomPropertyTypeLayout(
			FVCamInputDeviceID::StaticStruct()->GetFName(),
			FOnGetPropertyTypeCustomizationInstance::CreateStatic(&FVCamInputDeviceIDTypeCustomization::MakeInstance)
			);
		
		PropertyModule.RegisterCustomClassLayout(
			UVCamOutputProviderBase::StaticClass()->GetFName(),
			FOnGetDetailCustomizationInstance::CreateStatic(&FOutputProviderLayoutCustomization::MakeInstance)
		);
		PropertyModule.RegisterCustomClassLayout(
			AVCamBaseActor::StaticClass()->GetFName(),
			FOnGetDetailCustomizationInstance::CreateStatic(&FVCamBaseActorCustomization::MakeInstance)
		);
		PropertyModule.RegisterCustomClassLayout(
			UVCamStateSwitcherWidget::StaticClass()->GetFName(),
			FOnGetDetailCustomizationInstance::CreateStatic(&FVCamStateSwitcherWidgetCustomization::MakeInstance)
			);
	}

	void FVCamCoreEditorModule::RegisterDefaultConnectionRemappingCustomizations()
	{
		RegisterConnectionRemapCustomization(
			UVCamWidget::StaticClass(),
			FGetConnectionRemappingCustomization::CreateStatic(&FConnectionRemapCustomization_VCamWidget::Make)
			);
		RegisterConnectionRemapCustomization(
			UVCamStateSwitcherWidget::StaticClass(),
			FGetConnectionRemappingCustomization::CreateStatic(&FConnectionRemapCustomization_StateSwitcher::Make)
			);
	}

	void FVCamCoreEditorModule::UnregisterCustomizations()
	{
		if (FPropertyEditorModule* PropertyModule = FModuleManager::GetModulePtr<FPropertyEditorModule>("PropertyEditor"))
		{
			PropertyModule->UnregisterCustomPropertyTypeLayout(FVCamInputProfile::StaticStruct()->GetFName());
			PropertyModule->UnregisterCustomPropertyTypeLayout(FVCamConnectionTargetSettings::StaticStruct()->GetFName());
			PropertyModule->UnregisterCustomPropertyTypeLayout(FWidgetConnectionConfig::StaticStruct()->GetFName());
			PropertyModule->UnregisterCustomPropertyTypeLayout(UVCamOutputProviderBase::StaticClass()->GetFName());
			PropertyModule->UnregisterCustomPropertyTypeLayout(FVCamViewportLocker::StaticStruct()->GetFName());
			PropertyModule->UnregisterCustomPropertyTypeLayout(FChildWidgetReference::StaticStruct()->GetFName());
			PropertyModule->UnregisterCustomPropertyTypeLayout(FVCamChildWidgetReference::StaticStruct()->GetFName());
			PropertyModule->UnregisterCustomPropertyTypeLayout(FVCamInputDeviceID::StaticStruct()->GetFName());

			PropertyModule->UnregisterCustomClassLayout(UVCamOutputProviderBase::StaticClass()->GetFName());
			PropertyModule->UnregisterCustomClassLayout(AVCamBaseActor::StaticClass()->GetFName());
			PropertyModule->UnregisterCustomClassLayout(UVCamStateSwitcherWidget::StaticClass()->GetFName());
		}	
	}

	void FVCamCoreEditorModule::RegisterMultiUserFilters()
	{
		// By default MU will not transact all subobjects - an exception must be added
		if (const TSharedPtr<IConcertSyncClient> Client = IConcertSyncClientModule::Get().GetClient(TEXT("MultiUser")))
		{
			IConcertClientTransactionBridge* TransactionBridge = Client->GetTransactionBridge();
			TransactionBridge->RegisterTransactionFilter(
				TEXT("VCam"),
				FOnFilterTransactionDelegate::CreateRaw(this, &FVCamCoreEditorModule::ShouldObjectBeTransacted)
				);
		}
	}

	ETransactionFilterResult FVCamCoreEditorModule::ShouldObjectBeTransacted(const FConcertTransactionFilterArgs& FilterArgs) const
	{
		// This will allow output providers, modifiers, and the UVCamBlueprintAssetUserData
		const bool bIsInVCam = FilterArgs.ObjectToFilter && FilterArgs.ObjectToFilter->IsInA(UVCamComponent::StaticClass());
		return bIsInVCam ? ETransactionFilterResult::IncludeObject : ETransactionFilterResult::UseDefault;
	}
}

IMPLEMENT_MODULE(UE::VCamCoreEditor::FVCamCoreEditorModule, VCamCoreEditor);

	
#undef LOCTEXT_NAMESPACE
