# Portions of this file auto-generated by usdGenSchema.
# Edits will survive regeneration except for comments and
# changes to types with autoGenerated=true.
{
    "Plugins": [
        {
            "Info": {
                "Types": {
                    "UsdVolField3DAsset": {
                        "alias": {
                            "UsdSchemaBase": "Field3DAsset"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdVolFieldAsset"
                        ], 
                        "schemaKind": "concreteTyped"
                    }, 
                    "UsdVolFieldAsset": {
                        "alias": {
                            "UsdSchemaBase": "FieldAsset"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdVolFieldBase"
                        ], 
                        "schemaKind": "abstractTyped"
                    }, 
                    "UsdVolFieldBase": {
                        "alias": {
                            "UsdSchemaBase": "FieldBase"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdGeomXformable"
                        ], 
                        "schemaKind": "abstractTyped"
                    }, 
                    "UsdVolOpenVDBAsset": {
                        "alias": {
                            "UsdSchemaBase": "OpenVDBAsset"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdVolFieldAsset"
                        ], 
                        "schemaKind": "concreteTyped"
                    }, 
                    "UsdVolVolume": {
                        "alias": {
                            "UsdSchemaBase": "Volume"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdGeomGprim"
                        ], 
                        "schemaKind": "concreteTyped"
                    }
                }
            }, 
            "LibraryPath": "../../../../../../../../Binaries/Win64/usd_usdVol.dll", 
            "Name": "usdVol", 
            "ResourcePath": "resources", 
            "Root": "..", 
            "Type": "library"
        }
    ]
}
