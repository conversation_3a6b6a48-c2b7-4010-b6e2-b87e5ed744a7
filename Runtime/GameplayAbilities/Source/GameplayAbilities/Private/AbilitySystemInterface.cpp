// Copyright Epic Games, Inc. All Rights Reserved.

#include "AbilitySystemInterface.h"
#include "AbilitySystemReplicationProxyInterface.h"

#include UE_INLINE_GENERATED_CPP_BY_NAME(AbilitySystemInterface)

UAbilitySystemInterface::UAbilitySystemInterface(const FObjectInitializer& ObjectInitializer)
: Super(ObjectInitializer)
{
}

UAbilitySystemReplicationProxyInterface::UAbilitySystemReplicationProxyInterface(const FObjectInitializer& ObjectInitializer)
: Super(ObjectInitializer)
{
}

