// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

class UObjectBase;
class UObjectBaseUtility;
class	UObject;
class		UField;
class			UEnum;
class			FProperty;
class				FObjectProperty;
class			UStruct;
class				UFunction;
class				UClass;
class				UScriptStruct;
class		FLinker;
class			<PERSON>inkerLoad;
class			<PERSON>inkerSave;
class		UPackage;
class		USystem;
class		UTextBuffer;
class		UPackageMap;
class		UObjectRedirector;
class FField;
class	FProperty;
class		FByteProperty;
class		FUInt16Property;
class		FUInt32Property;
class		FUInt64Property;
class		FInt8Property;
class		FInt16Property;
class		FIntProperty;
class		FInt64Property;
class		FBoolProperty;
class		FFloatProperty;
class		FDoubleProperty;
class		FObjectPropertyBase;
class		FObjectProperty;
class			FClassProperty;
class			FInterfaceProperty;
class			FWeakObjectProperty;
class			FLazyObjectProperty;
class			FSoftObjectProperty;
class				FSoftClassProperty;
class		FNameProperty;
class		FStructProperty;
class		FStrProperty;
class		FTextProperty;
class		FArrayProperty;
class		FDelegateProperty;
class		FMulticastDelegateProperty;
class		FMapProperty;
class		FSetProperty;
class		FEnumProperty;
class		FUtf8StrProperty;
class		FAnsiStrProperty;
