<?xml version="1.0" encoding="utf-8"?>
<TpsData xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Name>Sandbox</Name>
  <Location>/Engine/Source/Runtime/Engine/Private/Animation/AnimPhysicsSolver.cpp</Location>
  <Date>2016-06-15T17:31:48.3370783-04:00</Date>
  <Function>simple physics engine </Function>
  <Justification>just for educational reference when creating similar implementations within the Unreal engine</Justification>
  <Eula>Web URL not available</Eula>
  <RedistributeTo>
    <EndUserGroup>Licensees</EndUserGroup>
    <EndUserGroup>Git</EndUserGroup>
    <EndUserGroup>P4</EndUserGroup>
  </RedistributeTo>
  <LicenseFolder>None</LicenseFolder>
</TpsData>