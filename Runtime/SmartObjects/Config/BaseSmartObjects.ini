[/Script/SmartObjectsModule.SmartObjectSubsystem]
SpacePartitionClassName=/Script/SmartObjectsModule.SmartObjectHashGrid

[CoreRedirects]
+FunctionRedirects=(OldName="K2_SetSmartObjectEnabled",NewName="AddOrRemoveSmartObject")

+EnumRedirects=(OldName="ESmartObjectChangeReason",ValueChanges=(("OnEnabled","OnSlotEnabled")))
+EnumRedirects=(OldName="ESmartObjectChangeReason",ValueChanges=(("OnDisabled","OnSlotDisabled")))

+StructRedirects=(OldName="SmartObjectSlotEntryAnnotation",NewName="/script/SmartObjectsModule.SmartObjectSlotEntranceAnnotation")
+StructRedirects=(OldName="SmartObjectSlotDefinitionDataProxy",NewName="/script/SmartObjectsModule.SmartObjectDefinitionDataProxy")
+StructRedirects=(OldName="SmartObjectSlotDefinitionData",NewName="/script/SmartObjectsModule.SmartObjectDefinitionData")
