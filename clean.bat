@echo off
echo ***********************************************************************
echo ********************** Clean Unreal Engine Project ********************
echo ***********************************************************************
echo.
:PROMPT
SET /P AREYOUSURE=Delete Binaries, Build, Intermediate, Saved, DerivedDataCache, .idea, .vs, .vscode folders, VS user/db files, OS thumbnail cruft, RiderLink, and project configs? (y/[N])
IF /I NOT "%AREYOUSURE%"=="y" GOTO END
echo.
echo Cleaning your project—this may take a moment...
echo.
REM — delete all matching directories (recursively)
FOR /d /r %%d IN (
  "Binaries","Build","Intermediate","Saved","DerivedDataCache",
  ".idea",".vs",".vscode"
) DO (
  IF EXIST "%%d" (
    attrib -R -S -H "%%d\*.*" /S /D
    rd /s /q "%%d"
  )
)
REM — delete RiderLink folder if it exists
IF EXIST "Plugins\Developer\RiderLink" (
  attrib -R -S -H "Plugins\Developer\RiderLink\*.*" /S /D
  rd /s /q "Plugins\Developer\RiderLink"
)
REM — delete solution files and config
attrib -R -S -H *.sln
del /f /q *.sln
IF EXIST ".vsconfig" (
  attrib -R -S -H ".vsconfig"
  del /f /q ".vsconfig"
)
REM — clean up Visual Studio user & database files
attrib -R -S -H *.suo *.user *.opensdf *.sdf *.VC.db /S
del /s /f /q *.suo *.user *.opensdf *.sdf *.VC.db
REM — clean up OS thumbnail/index cruft
attrib -R -S -H Thumbs.db /S
del /s /f /q Thumbs.db
attrib -R -S -H .DS_Store /S
del /s /f /q .DS_Store
echo.
echo Setting read-only attributes on remaining files for Perforce compatibility...
REM — Set read-only attribute on all remaining files (excluding directories)
attrib +R *.* /S
REM — Set read-only attribute on all files in subdirectories
FOR /R %%f IN (*.*) DO (
  IF NOT EXIST "%%f\" (
    attrib +R "%%f"
  )
)
echo.
echo ✅ Project cleaned and files set to read-only for Perforce! You can now regenerate your project files.
pause
:END